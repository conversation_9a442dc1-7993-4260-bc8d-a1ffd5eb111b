{"version": 2, "dgSpecHash": "1Qb+W/IhKa0=", "success": true, "projectFilePath": "/Users/<USER>/vidcompressor/worker/worker.csproj", "expectedPackageFiles": ["/Users/<USER>/.nuget/packages/microsoft.extensions.configuration/9.0.6/microsoft.extensions.configuration.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.abstractions/9.0.6/microsoft.extensions.configuration.abstractions.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.binder/9.0.6/microsoft.extensions.configuration.binder.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.commandline/9.0.6/microsoft.extensions.configuration.commandline.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.environmentvariables/9.0.6/microsoft.extensions.configuration.environmentvariables.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.fileextensions/9.0.6/microsoft.extensions.configuration.fileextensions.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.json/9.0.6/microsoft.extensions.configuration.json.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.usersecrets/9.0.6/microsoft.extensions.configuration.usersecrets.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection/9.0.6/microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/9.0.6/microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.diagnostics/9.0.6/microsoft.extensions.diagnostics.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.diagnostics.abstractions/9.0.6/microsoft.extensions.diagnostics.abstractions.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.fileproviders.abstractions/9.0.6/microsoft.extensions.fileproviders.abstractions.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.fileproviders.physical/9.0.6/microsoft.extensions.fileproviders.physical.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.filesystemglobbing/9.0.6/microsoft.extensions.filesystemglobbing.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.hosting/9.0.6/microsoft.extensions.hosting.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.hosting.abstractions/9.0.6/microsoft.extensions.hosting.abstractions.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging/9.0.6/microsoft.extensions.logging.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/9.0.6/microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.configuration/9.0.6/microsoft.extensions.logging.configuration.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.console/9.0.6/microsoft.extensions.logging.console.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.debug/9.0.6/microsoft.extensions.logging.debug.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.eventlog/9.0.6/microsoft.extensions.logging.eventlog.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.eventsource/9.0.6/microsoft.extensions.logging.eventsource.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options/9.0.6/microsoft.extensions.options.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options.configurationextensions/9.0.6/microsoft.extensions.options.configurationextensions.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.primitives/9.0.6/microsoft.extensions.primitives.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/opentelemetry/1.12.0/opentelemetry.1.12.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/opentelemetry.api/1.12.0/opentelemetry.api.1.12.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/opentelemetry.api.providerbuilderextensions/1.12.0/opentelemetry.api.providerbuilderextensions.1.12.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/opentelemetry.exporter.console/1.12.0/opentelemetry.exporter.console.1.12.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/opentelemetry.extensions.hosting/1.12.0/opentelemetry.extensions.hosting.1.12.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/opentelemetry.instrumentation.aspnetcore/1.12.0/opentelemetry.instrumentation.aspnetcore.1.12.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/opentelemetry.instrumentation.http/1.12.0/opentelemetry.instrumentation.http.1.12.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/opentelemetry.instrumentation.stackexchangeredis/1.12.0-beta.1/opentelemetry.instrumentation.stackexchangeredis.1.12.0-beta.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/pipelines.sockets.unofficial/2.2.8/pipelines.sockets.unofficial.2.2.8.nupkg.sha512", "/Users/<USER>/.nuget/packages/stackexchange.redis/2.8.41/stackexchange.redis.2.8.41.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.diagnosticsource/9.0.6/system.diagnostics.diagnosticsource.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.eventlog/9.0.6/system.diagnostics.eventlog.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.pipelines/9.0.6/system.io.pipelines.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encodings.web/9.0.6/system.text.encodings.web.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.json/9.0.6/system.text.json.9.0.6.nupkg.sha512"], "logs": []}