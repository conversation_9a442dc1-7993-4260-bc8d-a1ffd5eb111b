{"format": 1, "restore": {"/Users/<USER>/vidcompressor/worker/worker.csproj": {}}, "projects": {"/Users/<USER>/vidcompressor/worker/worker.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/vidcompressor/worker/worker.csproj", "projectName": "worker", "projectPath": "/Users/<USER>/vidcompressor/worker/worker.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/vidcompressor/worker/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.6, )"}, "OpenTelemetry.Exporter.Console": {"target": "Package", "version": "[1.12.0, )"}, "OpenTelemetry.Extensions.Hosting": {"target": "Package", "version": "[1.12.0, )"}, "OpenTelemetry.Instrumentation.AspNetCore": {"target": "Package", "version": "[1.12.0, )"}, "OpenTelemetry.Instrumentation.Http": {"target": "Package", "version": "[1.12.0, )"}, "OpenTelemetry.Instrumentation.StackExchangeRedis": {"target": "Package", "version": "[1.12.0-beta.1, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.8.41, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.403/PortableRuntimeIdentifierGraph.json"}}}}}