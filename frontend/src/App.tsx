import React, { useState } from 'react';
import { GoogleOAuthProvider, GoogleLogin, CredentialResponse } from '@react-oauth/google';
import { AppBar, Toolbar, Typography, Button, Container, Grid, Paper } from '@mui/material';

const App: React.FC = () => {
  const [user, setUser] = useState<CredentialResponse | null>(null);

  const handleLoginSuccess = (credentialResponse: CredentialResponse) => {
    setUser(credentialResponse);
    // Here you would typically send the token to your backend for verification
    console.log('Login Success:', credentialResponse);
  };

  const handleLoginError = () => {
    console.log('Login Failed');
  };

  const handleLogout = () => {
    setUser(null);
  };

  return (
    <GoogleOAuthProvider clientId="YOUR_GOOGLE_CLIENT_ID">
      <AppBar position="static">
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            VidCompressor
          </Typography>
          {user ? (
            <Button color="inherit" onClick={handleLogout}>Logout</Button>
          ) : (
            <GoogleLogin onSuccess={handleLoginSuccess} onError={handleLoginError} />
          )}
        </Toolbar>
      </AppBar>
      <Container sx={{ mt: 4 }}>
        {user ? (
          <div>
            <Typography variant="h4" gutterBottom>
              Your Video Library
            </Typography>
            <Grid container spacing={2}>
              {/* Placeholder for video gallery */}
              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Paper sx={{ p: 2 }}>
                  <Typography>Video 1</Typography>
                </Paper>
              </Grid>
              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Paper sx={{ p: 2 }}>
                  <Typography>Video 2</Typography>
                </Paper>
              </Grid>
              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Paper sx={{ p: 2 }}>
                  <Typography>Video 3</Typography>
                </Paper>
              </Grid>
            </Grid>
          </div>
        ) : (
          <Typography variant="h5" align="center" sx={{ mt: 10 }}>
            Please sign in with your Google account to continue.
          </Typography>
        )}
      </Container>
    </GoogleOAuthProvider>
  );
};

export default App;
