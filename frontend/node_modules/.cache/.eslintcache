[{"/Users/<USER>/vidcompressor/frontend/src/index.tsx": "1", "/Users/<USER>/vidcompressor/frontend/src/reportWebVitals.ts": "2", "/Users/<USER>/vidcompressor/frontend/src/App.tsx": "3"}, {"size": 554, "mtime": 1751848843368, "results": "4", "hashOfConfig": "5"}, {"size": 425, "mtime": 1751848843369, "results": "6", "hashOfConfig": "5"}, {"size": 2325, "mtime": 1751851954150, "results": "7", "hashOfConfig": "5"}, {"filePath": "8", "messages": "9", "suppressedMessages": "10", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1rpa79t", {"filePath": "11", "messages": "12", "suppressedMessages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/vidcompressor/frontend/src/index.tsx", [], [], "/Users/<USER>/vidcompressor/frontend/src/reportWebVitals.ts", [], [], "/Users/<USER>/vidcompressor/frontend/src/App.tsx", [], []]