{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { ThemeProvider as SystemThemeProvider } from '@mui/system';\nimport THEME_ID from \"./identifier.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function ThemeProviderNoVars({\n  theme: themeInput,\n  ...props\n}) {\n  const scopedTheme = THEME_ID in themeInput ? themeInput[THEME_ID] : undefined;\n  return /*#__PURE__*/_jsx(SystemThemeProvider, {\n    ...props,\n    themeId: scopedTheme ? THEME_ID : undefined,\n    theme: scopedTheme || themeInput\n  });\n}", "map": {"version": 3, "names": ["React", "ThemeProvider", "SystemThemeProvider", "THEME_ID", "jsx", "_jsx", "ThemeProviderNoVars", "theme", "themeInput", "props", "scopedTheme", "undefined", "themeId"], "sources": ["/Users/<USER>/vidcompressor/frontend/node_modules/@mui/material/esm/styles/ThemeProviderNoVars.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { ThemeProvider as SystemThemeProvider } from '@mui/system';\nimport THEME_ID from \"./identifier.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function ThemeProviderNoVars({\n  theme: themeInput,\n  ...props\n}) {\n  const scopedTheme = THEME_ID in themeInput ? themeInput[THEME_ID] : undefined;\n  return /*#__PURE__*/_jsx(SystemThemeProvider, {\n    ...props,\n    themeId: scopedTheme ? THEME_ID : undefined,\n    theme: scopedTheme || themeInput\n  });\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,IAAIC,mBAAmB,QAAQ,aAAa;AAClE,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAe,SAASC,mBAAmBA,CAAC;EAC1CC,KAAK,EAAEC,UAAU;EACjB,GAAGC;AACL,CAAC,EAAE;EACD,MAAMC,WAAW,GAAGP,QAAQ,IAAIK,UAAU,GAAGA,UAAU,CAACL,QAAQ,CAAC,GAAGQ,SAAS;EAC7E,OAAO,aAAaN,IAAI,CAACH,mBAAmB,EAAE;IAC5C,GAAGO,KAAK;IACRG,OAAO,EAAEF,WAAW,GAAGP,QAAQ,GAAGQ,SAAS;IAC3CJ,KAAK,EAAEG,WAAW,IAAIF;EACxB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}