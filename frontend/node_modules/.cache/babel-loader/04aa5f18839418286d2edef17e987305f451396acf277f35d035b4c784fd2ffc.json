{"ast": null, "code": "var hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nexport default function getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}", "map": {"version": 3, "names": ["hash", "left", "right", "bottom", "top", "getOppositePlacement", "placement", "replace", "matched"], "sources": ["/Users/<USER>/vidcompressor/frontend/node_modules/@popperjs/core/lib/utils/getOppositePlacement.js"], "sourcesContent": ["var hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nexport default function getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}"], "mappings": "AAAA,IAAIA,IAAI,GAAG;EACTC,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE,KAAK;EACbC,GAAG,EAAE;AACP,CAAC;AACD,eAAe,SAASC,oBAAoBA,CAACC,SAAS,EAAE;EACtD,OAAOA,SAAS,CAACC,OAAO,CAAC,wBAAwB,EAAE,UAAUC,OAAO,EAAE;IACpE,OAAOR,IAAI,CAACQ,OAAO,CAAC;EACtB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}