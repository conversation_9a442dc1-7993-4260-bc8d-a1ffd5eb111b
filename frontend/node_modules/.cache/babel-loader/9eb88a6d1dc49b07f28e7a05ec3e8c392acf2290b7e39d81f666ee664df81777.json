{"ast": null, "code": "import responsivePropType from \"../responsivePropType/index.js\";\nimport style from \"../style/index.js\";\nimport compose from \"../compose/index.js\";\nimport { createUnaryUnit, getValue } from \"../spacing/index.js\";\nimport { handleBreakpoints } from \"../breakpoints/index.js\";\nexport function borderTransform(value) {\n  if (typeof value !== 'number') {\n    return value;\n  }\n  return `${value}px solid`;\n}\nfunction createBorderStyle(prop, transform) {\n  return style({\n    prop,\n    themeKey: 'borders',\n    transform\n  });\n}\nexport const border = createBorderStyle('border', borderTransform);\nexport const borderTop = createBorderStyle('borderTop', borderTransform);\nexport const borderRight = createBorderStyle('borderRight', borderTransform);\nexport const borderBottom = createBorderStyle('borderBottom', borderTransform);\nexport const borderLeft = createBorderStyle('borderLeft', borderTransform);\nexport const borderColor = createBorderStyle('borderColor');\nexport const borderTopColor = createBorderStyle('borderTopColor');\nexport const borderRightColor = createBorderStyle('borderRightColor');\nexport const borderBottomColor = createBorderStyle('borderBottomColor');\nexport const borderLeftColor = createBorderStyle('borderLeftColor');\nexport const outline = createBorderStyle('outline', borderTransform);\nexport const outlineColor = createBorderStyle('outlineColor');\n\n// false positive\n// eslint-disable-next-line react/function-component-definition\nexport const borderRadius = props => {\n  if (props.borderRadius !== undefined && props.borderRadius !== null) {\n    const transformer = createUnaryUnit(props.theme, 'shape.borderRadius', 4, 'borderRadius');\n    const styleFromPropValue = propValue => ({\n      borderRadius: getValue(transformer, propValue)\n    });\n    return handleBreakpoints(props, props.borderRadius, styleFromPropValue);\n  }\n  return null;\n};\nborderRadius.propTypes = process.env.NODE_ENV !== 'production' ? {\n  borderRadius: responsivePropType\n} : {};\nborderRadius.filterProps = ['borderRadius'];\nconst borders = compose(border, borderTop, borderRight, borderBottom, borderLeft, borderColor, borderTopColor, borderRightColor, borderBottomColor, borderLeftColor, borderRadius, outline, outlineColor);\nexport default borders;", "map": {"version": 3, "names": ["responsivePropType", "style", "compose", "createUnaryUnit", "getValue", "handleBreakpoints", "borderTransform", "value", "createBorderStyle", "prop", "transform", "<PERSON><PERSON><PERSON>", "border", "borderTop", "borderRight", "borderBottom", "borderLeft", "borderColor", "borderTopColor", "borderRightColor", "borderBottomColor", "borderLeftColor", "outline", "outlineColor", "borderRadius", "props", "undefined", "transformer", "theme", "styleFromPropValue", "propValue", "propTypes", "process", "env", "NODE_ENV", "filterProps", "borders"], "sources": ["/Users/<USER>/vidcompressor/frontend/node_modules/@mui/system/esm/borders/borders.js"], "sourcesContent": ["import responsivePropType from \"../responsivePropType/index.js\";\nimport style from \"../style/index.js\";\nimport compose from \"../compose/index.js\";\nimport { createUnaryUnit, getValue } from \"../spacing/index.js\";\nimport { handleBreakpoints } from \"../breakpoints/index.js\";\nexport function borderTransform(value) {\n  if (typeof value !== 'number') {\n    return value;\n  }\n  return `${value}px solid`;\n}\nfunction createBorderStyle(prop, transform) {\n  return style({\n    prop,\n    themeKey: 'borders',\n    transform\n  });\n}\nexport const border = createBorderStyle('border', borderTransform);\nexport const borderTop = createBorderStyle('borderTop', borderTransform);\nexport const borderRight = createBorderStyle('borderRight', borderTransform);\nexport const borderBottom = createBorderStyle('borderBottom', borderTransform);\nexport const borderLeft = createBorderStyle('borderLeft', borderTransform);\nexport const borderColor = createBorderStyle('borderColor');\nexport const borderTopColor = createBorderStyle('borderTopColor');\nexport const borderRightColor = createBorderStyle('borderRightColor');\nexport const borderBottomColor = createBorderStyle('borderBottomColor');\nexport const borderLeftColor = createBorderStyle('borderLeftColor');\nexport const outline = createBorderStyle('outline', borderTransform);\nexport const outlineColor = createBorderStyle('outlineColor');\n\n// false positive\n// eslint-disable-next-line react/function-component-definition\nexport const borderRadius = props => {\n  if (props.borderRadius !== undefined && props.borderRadius !== null) {\n    const transformer = createUnaryUnit(props.theme, 'shape.borderRadius', 4, 'borderRadius');\n    const styleFromPropValue = propValue => ({\n      borderRadius: getValue(transformer, propValue)\n    });\n    return handleBreakpoints(props, props.borderRadius, styleFromPropValue);\n  }\n  return null;\n};\nborderRadius.propTypes = process.env.NODE_ENV !== 'production' ? {\n  borderRadius: responsivePropType\n} : {};\nborderRadius.filterProps = ['borderRadius'];\nconst borders = compose(border, borderTop, borderRight, borderBottom, borderLeft, borderColor, borderTopColor, borderRightColor, borderBottomColor, borderLeftColor, borderRadius, outline, outlineColor);\nexport default borders;"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,gCAAgC;AAC/D,OAAOC,KAAK,MAAM,mBAAmB;AACrC,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,eAAe,EAAEC,QAAQ,QAAQ,qBAAqB;AAC/D,SAASC,iBAAiB,QAAQ,yBAAyB;AAC3D,OAAO,SAASC,eAAeA,CAACC,KAAK,EAAE;EACrC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOA,KAAK;EACd;EACA,OAAO,GAAGA,KAAK,UAAU;AAC3B;AACA,SAASC,iBAAiBA,CAACC,IAAI,EAAEC,SAAS,EAAE;EAC1C,OAAOT,KAAK,CAAC;IACXQ,IAAI;IACJE,QAAQ,EAAE,SAAS;IACnBD;EACF,CAAC,CAAC;AACJ;AACA,OAAO,MAAME,MAAM,GAAGJ,iBAAiB,CAAC,QAAQ,EAAEF,eAAe,CAAC;AAClE,OAAO,MAAMO,SAAS,GAAGL,iBAAiB,CAAC,WAAW,EAAEF,eAAe,CAAC;AACxE,OAAO,MAAMQ,WAAW,GAAGN,iBAAiB,CAAC,aAAa,EAAEF,eAAe,CAAC;AAC5E,OAAO,MAAMS,YAAY,GAAGP,iBAAiB,CAAC,cAAc,EAAEF,eAAe,CAAC;AAC9E,OAAO,MAAMU,UAAU,GAAGR,iBAAiB,CAAC,YAAY,EAAEF,eAAe,CAAC;AAC1E,OAAO,MAAMW,WAAW,GAAGT,iBAAiB,CAAC,aAAa,CAAC;AAC3D,OAAO,MAAMU,cAAc,GAAGV,iBAAiB,CAAC,gBAAgB,CAAC;AACjE,OAAO,MAAMW,gBAAgB,GAAGX,iBAAiB,CAAC,kBAAkB,CAAC;AACrE,OAAO,MAAMY,iBAAiB,GAAGZ,iBAAiB,CAAC,mBAAmB,CAAC;AACvE,OAAO,MAAMa,eAAe,GAAGb,iBAAiB,CAAC,iBAAiB,CAAC;AACnE,OAAO,MAAMc,OAAO,GAAGd,iBAAiB,CAAC,SAAS,EAAEF,eAAe,CAAC;AACpE,OAAO,MAAMiB,YAAY,GAAGf,iBAAiB,CAAC,cAAc,CAAC;;AAE7D;AACA;AACA,OAAO,MAAMgB,YAAY,GAAGC,KAAK,IAAI;EACnC,IAAIA,KAAK,CAACD,YAAY,KAAKE,SAAS,IAAID,KAAK,CAACD,YAAY,KAAK,IAAI,EAAE;IACnE,MAAMG,WAAW,GAAGxB,eAAe,CAACsB,KAAK,CAACG,KAAK,EAAE,oBAAoB,EAAE,CAAC,EAAE,cAAc,CAAC;IACzF,MAAMC,kBAAkB,GAAGC,SAAS,KAAK;MACvCN,YAAY,EAAEpB,QAAQ,CAACuB,WAAW,EAAEG,SAAS;IAC/C,CAAC,CAAC;IACF,OAAOzB,iBAAiB,CAACoB,KAAK,EAAEA,KAAK,CAACD,YAAY,EAAEK,kBAAkB,CAAC;EACzE;EACA,OAAO,IAAI;AACb,CAAC;AACDL,YAAY,CAACO,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG;EAC/DV,YAAY,EAAExB;AAChB,CAAC,GAAG,CAAC,CAAC;AACNwB,YAAY,CAACW,WAAW,GAAG,CAAC,cAAc,CAAC;AAC3C,MAAMC,OAAO,GAAGlC,OAAO,CAACU,MAAM,EAAEC,SAAS,EAAEC,WAAW,EAAEC,YAAY,EAAEC,UAAU,EAAEC,WAAW,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,eAAe,EAAEG,YAAY,EAAEF,OAAO,EAAEC,YAAY,CAAC;AACzM,eAAea,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}