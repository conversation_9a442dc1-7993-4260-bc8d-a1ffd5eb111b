{"ast": null, "code": "export { default } from \"./responsivePropType.js\";", "map": {"version": 3, "names": ["default"], "sources": ["/Users/<USER>/vidcompressor/frontend/node_modules/@mui/system/esm/responsivePropType/index.js"], "sourcesContent": ["export { default } from \"./responsivePropType.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}