{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\n\n/**\n * @ignore - internal component.\n */\nconst GridLegacyContext = /*#__PURE__*/React.createContext();\nif (process.env.NODE_ENV !== 'production') {\n  GridLegacyContext.displayName = 'GridLegacyContext';\n}\nexport default GridLegacyContext;", "map": {"version": 3, "names": ["React", "GridLegacyContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/vidcompressor/frontend/node_modules/@mui/material/esm/GridLegacy/GridLegacyContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\n\n/**\n * @ignore - internal component.\n */\nconst GridLegacyContext = /*#__PURE__*/React.createContext();\nif (process.env.NODE_ENV !== 'production') {\n  GridLegacyContext.displayName = 'GridLegacyContext';\n}\nexport default GridLegacyContext;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC;AAC5D,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,iBAAiB,CAACK,WAAW,GAAG,mBAAmB;AACrD;AACA,eAAeL,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}