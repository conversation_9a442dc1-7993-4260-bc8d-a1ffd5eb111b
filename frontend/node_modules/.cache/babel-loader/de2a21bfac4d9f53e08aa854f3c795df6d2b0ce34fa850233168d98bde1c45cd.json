{"ast": null, "code": "'use client';\n\nvar _span;\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport rootShouldForwardProp from \"../styles/rootShouldForwardProp.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst NotchedOutlineRoot = styled('fieldset', {\n  name: 'MuiNotchedOutlined',\n  shouldForwardProp: rootShouldForwardProp\n})({\n  textAlign: 'left',\n  position: 'absolute',\n  bottom: 0,\n  right: 0,\n  top: -5,\n  left: 0,\n  margin: 0,\n  padding: '0 8px',\n  pointerEvents: 'none',\n  borderRadius: 'inherit',\n  borderStyle: 'solid',\n  borderWidth: 1,\n  overflow: 'hidden',\n  minWidth: '0%'\n});\nconst NotchedOutlineLegend = styled('legend', {\n  name: '<PERSON>iNotchedOutlined',\n  shouldForwardProp: rootShouldForwardProp\n})(memoTheme(({\n  theme\n}) => ({\n  float: 'unset',\n  // Fix conflict with bootstrap\n  width: 'auto',\n  // Fix conflict with bootstrap\n  overflow: 'hidden',\n  // Fix Horizontal scroll when label too long\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.withLabel,\n    style: {\n      padding: 0,\n      lineHeight: '11px',\n      // sync with `height` in `legend` styles\n      transition: theme.transitions.create('width', {\n        duration: 150,\n        easing: theme.transitions.easing.easeOut\n      })\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.withLabel,\n    style: {\n      display: 'block',\n      // Fix conflict with normalize.css and sanitize.css\n      padding: 0,\n      height: 11,\n      // sync with `lineHeight` in `legend` styles\n      fontSize: '0.75em',\n      visibility: 'hidden',\n      maxWidth: 0.01,\n      transition: theme.transitions.create('max-width', {\n        duration: 50,\n        easing: theme.transitions.easing.easeOut\n      }),\n      whiteSpace: 'nowrap',\n      '& > span': {\n        paddingLeft: 5,\n        paddingRight: 5,\n        display: 'inline-block',\n        opacity: 0,\n        visibility: 'visible'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.withLabel && ownerState.notched,\n    style: {\n      maxWidth: '100%',\n      transition: theme.transitions.create('max-width', {\n        duration: 100,\n        easing: theme.transitions.easing.easeOut,\n        delay: 50\n      })\n    }\n  }]\n})));\n\n/**\n * @ignore - internal component.\n */\nexport default function NotchedOutline(props) {\n  const {\n    children,\n    classes,\n    className,\n    label,\n    notched,\n    ...other\n  } = props;\n  const withLabel = label != null && label !== '';\n  const ownerState = {\n    ...props,\n    notched,\n    withLabel\n  };\n  return /*#__PURE__*/_jsx(NotchedOutlineRoot, {\n    \"aria-hidden\": true,\n    className: className,\n    ownerState: ownerState,\n    ...other,\n    children: /*#__PURE__*/_jsx(NotchedOutlineLegend, {\n      ownerState: ownerState,\n      children: withLabel ? /*#__PURE__*/_jsx(\"span\", {\n        children: label\n      }) :\n      // notranslate needed while Google Translate will not fix zero-width space issue\n      _span || (_span = /*#__PURE__*/_jsx(\"span\", {\n        className: \"notranslate\",\n        \"aria-hidden\": true,\n        children: \"\\u200B\"\n      }))\n    })\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? NotchedOutline.propTypes /* remove-proptypes */ = {\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The label.\n   */\n  label: PropTypes.node,\n  /**\n   * If `true`, the outline is notched to accommodate the label.\n   */\n  notched: PropTypes.bool.isRequired,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object\n} : void 0;", "map": {"version": 3, "names": ["_span", "React", "PropTypes", "rootShouldForwardProp", "styled", "memoTheme", "jsx", "_jsx", "NotchedOutlineRoot", "name", "shouldForwardProp", "textAlign", "position", "bottom", "right", "top", "left", "margin", "padding", "pointerEvents", "borderRadius", "borderStyle", "borderWidth", "overflow", "min<PERSON><PERSON><PERSON>", "NotchedOutlineLegend", "theme", "float", "width", "variants", "props", "ownerState", "<PERSON><PERSON><PERSON><PERSON>", "style", "lineHeight", "transition", "transitions", "create", "duration", "easing", "easeOut", "display", "height", "fontSize", "visibility", "max<PERSON><PERSON><PERSON>", "whiteSpace", "paddingLeft", "paddingRight", "opacity", "notched", "delay", "NotchedOutline", "children", "classes", "className", "label", "other", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "bool", "isRequired"], "sources": ["/Users/<USER>/vidcompressor/frontend/node_modules/@mui/material/esm/OutlinedInput/NotchedOutline.js"], "sourcesContent": ["'use client';\n\nvar _span;\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport rootShouldForwardProp from \"../styles/rootShouldForwardProp.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst NotchedOutlineRoot = styled('fieldset', {\n  name: 'MuiNotchedOutlined',\n  shouldForwardProp: rootShouldForwardProp\n})({\n  textAlign: 'left',\n  position: 'absolute',\n  bottom: 0,\n  right: 0,\n  top: -5,\n  left: 0,\n  margin: 0,\n  padding: '0 8px',\n  pointerEvents: 'none',\n  borderRadius: 'inherit',\n  borderStyle: 'solid',\n  borderWidth: 1,\n  overflow: 'hidden',\n  minWidth: '0%'\n});\nconst NotchedOutlineLegend = styled('legend', {\n  name: '<PERSON>iNotchedOutlined',\n  shouldForwardProp: rootShouldForwardProp\n})(memoTheme(({\n  theme\n}) => ({\n  float: 'unset',\n  // Fix conflict with bootstrap\n  width: 'auto',\n  // Fix conflict with bootstrap\n  overflow: 'hidden',\n  // Fix Horizontal scroll when label too long\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.withLabel,\n    style: {\n      padding: 0,\n      lineHeight: '11px',\n      // sync with `height` in `legend` styles\n      transition: theme.transitions.create('width', {\n        duration: 150,\n        easing: theme.transitions.easing.easeOut\n      })\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.withLabel,\n    style: {\n      display: 'block',\n      // Fix conflict with normalize.css and sanitize.css\n      padding: 0,\n      height: 11,\n      // sync with `lineHeight` in `legend` styles\n      fontSize: '0.75em',\n      visibility: 'hidden',\n      maxWidth: 0.01,\n      transition: theme.transitions.create('max-width', {\n        duration: 50,\n        easing: theme.transitions.easing.easeOut\n      }),\n      whiteSpace: 'nowrap',\n      '& > span': {\n        paddingLeft: 5,\n        paddingRight: 5,\n        display: 'inline-block',\n        opacity: 0,\n        visibility: 'visible'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.withLabel && ownerState.notched,\n    style: {\n      maxWidth: '100%',\n      transition: theme.transitions.create('max-width', {\n        duration: 100,\n        easing: theme.transitions.easing.easeOut,\n        delay: 50\n      })\n    }\n  }]\n})));\n\n/**\n * @ignore - internal component.\n */\nexport default function NotchedOutline(props) {\n  const {\n    children,\n    classes,\n    className,\n    label,\n    notched,\n    ...other\n  } = props;\n  const withLabel = label != null && label !== '';\n  const ownerState = {\n    ...props,\n    notched,\n    withLabel\n  };\n  return /*#__PURE__*/_jsx(NotchedOutlineRoot, {\n    \"aria-hidden\": true,\n    className: className,\n    ownerState: ownerState,\n    ...other,\n    children: /*#__PURE__*/_jsx(NotchedOutlineLegend, {\n      ownerState: ownerState,\n      children: withLabel ? /*#__PURE__*/_jsx(\"span\", {\n        children: label\n      }) : // notranslate needed while Google Translate will not fix zero-width space issue\n      _span || (_span = /*#__PURE__*/_jsx(\"span\", {\n        className: \"notranslate\",\n        \"aria-hidden\": true,\n        children: \"\\u200B\"\n      }))\n    })\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? NotchedOutline.propTypes /* remove-proptypes */ = {\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The label.\n   */\n  label: PropTypes.node,\n  /**\n   * If `true`, the outline is notched to accommodate the label.\n   */\n  notched: PropTypes.bool.isRequired,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object\n} : void 0;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK;AACT,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,qBAAqB,MAAM,oCAAoC;AACtE,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,kBAAkB,GAAGJ,MAAM,CAAC,UAAU,EAAE;EAC5CK,IAAI,EAAE,oBAAoB;EAC1BC,iBAAiB,EAAEP;AACrB,CAAC,CAAC,CAAC;EACDQ,SAAS,EAAE,MAAM;EACjBC,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,CAAC;EACRC,GAAG,EAAE,CAAC,CAAC;EACPC,IAAI,EAAE,CAAC;EACPC,MAAM,EAAE,CAAC;EACTC,OAAO,EAAE,OAAO;EAChBC,aAAa,EAAE,MAAM;EACrBC,YAAY,EAAE,SAAS;EACvBC,WAAW,EAAE,OAAO;EACpBC,WAAW,EAAE,CAAC;EACdC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF,MAAMC,oBAAoB,GAAGrB,MAAM,CAAC,QAAQ,EAAE;EAC5CK,IAAI,EAAE,oBAAoB;EAC1BC,iBAAiB,EAAEP;AACrB,CAAC,CAAC,CAACE,SAAS,CAAC,CAAC;EACZqB;AACF,CAAC,MAAM;EACLC,KAAK,EAAE,OAAO;EACd;EACAC,KAAK,EAAE,MAAM;EACb;EACAL,QAAQ,EAAE,QAAQ;EAClB;EACAM,QAAQ,EAAE,CAAC;IACTC,KAAK,EAAEA,CAAC;MACNC;IACF,CAAC,KAAK,CAACA,UAAU,CAACC,SAAS;IAC3BC,KAAK,EAAE;MACLf,OAAO,EAAE,CAAC;MACVgB,UAAU,EAAE,MAAM;MAClB;MACAC,UAAU,EAAET,KAAK,CAACU,WAAW,CAACC,MAAM,CAAC,OAAO,EAAE;QAC5CC,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAEb,KAAK,CAACU,WAAW,CAACG,MAAM,CAACC;MACnC,CAAC;IACH;EACF,CAAC,EAAE;IACDV,KAAK,EAAEA,CAAC;MACNC;IACF,CAAC,KAAKA,UAAU,CAACC,SAAS;IAC1BC,KAAK,EAAE;MACLQ,OAAO,EAAE,OAAO;MAChB;MACAvB,OAAO,EAAE,CAAC;MACVwB,MAAM,EAAE,EAAE;MACV;MACAC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,QAAQ;MACpBC,QAAQ,EAAE,IAAI;MACdV,UAAU,EAAET,KAAK,CAACU,WAAW,CAACC,MAAM,CAAC,WAAW,EAAE;QAChDC,QAAQ,EAAE,EAAE;QACZC,MAAM,EAAEb,KAAK,CAACU,WAAW,CAACG,MAAM,CAACC;MACnC,CAAC,CAAC;MACFM,UAAU,EAAE,QAAQ;MACpB,UAAU,EAAE;QACVC,WAAW,EAAE,CAAC;QACdC,YAAY,EAAE,CAAC;QACfP,OAAO,EAAE,cAAc;QACvBQ,OAAO,EAAE,CAAC;QACVL,UAAU,EAAE;MACd;IACF;EACF,CAAC,EAAE;IACDd,KAAK,EAAEA,CAAC;MACNC;IACF,CAAC,KAAKA,UAAU,CAACC,SAAS,IAAID,UAAU,CAACmB,OAAO;IAChDjB,KAAK,EAAE;MACLY,QAAQ,EAAE,MAAM;MAChBV,UAAU,EAAET,KAAK,CAACU,WAAW,CAACC,MAAM,CAAC,WAAW,EAAE;QAChDC,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAEb,KAAK,CAACU,WAAW,CAACG,MAAM,CAACC,OAAO;QACxCW,KAAK,EAAE;MACT,CAAC;IACH;EACF,CAAC;AACH,CAAC,CAAC,CAAC,CAAC;;AAEJ;AACA;AACA;AACA,eAAe,SAASC,cAAcA,CAACtB,KAAK,EAAE;EAC5C,MAAM;IACJuB,QAAQ;IACRC,OAAO;IACPC,SAAS;IACTC,KAAK;IACLN,OAAO;IACP,GAAGO;EACL,CAAC,GAAG3B,KAAK;EACT,MAAME,SAAS,GAAGwB,KAAK,IAAI,IAAI,IAAIA,KAAK,KAAK,EAAE;EAC/C,MAAMzB,UAAU,GAAG;IACjB,GAAGD,KAAK;IACRoB,OAAO;IACPlB;EACF,CAAC;EACD,OAAO,aAAazB,IAAI,CAACC,kBAAkB,EAAE;IAC3C,aAAa,EAAE,IAAI;IACnB+C,SAAS,EAAEA,SAAS;IACpBxB,UAAU,EAAEA,UAAU;IACtB,GAAG0B,KAAK;IACRJ,QAAQ,EAAE,aAAa9C,IAAI,CAACkB,oBAAoB,EAAE;MAChDM,UAAU,EAAEA,UAAU;MACtBsB,QAAQ,EAAErB,SAAS,GAAG,aAAazB,IAAI,CAAC,MAAM,EAAE;QAC9C8C,QAAQ,EAAEG;MACZ,CAAC,CAAC;MAAG;MACLxD,KAAK,KAAKA,KAAK,GAAG,aAAaO,IAAI,CAAC,MAAM,EAAE;QAC1CgD,SAAS,EAAE,aAAa;QACxB,aAAa,EAAE,IAAI;QACnBF,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,CAAC;AACJ;AACAK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGR,cAAc,CAACS,SAAS,CAAC,yBAAyB;EACxF;AACF;AACA;EACER,QAAQ,EAAEnD,SAAS,CAAC4D,IAAI;EACxB;AACF;AACA;EACER,OAAO,EAAEpD,SAAS,CAAC6D,MAAM;EACzB;AACF;AACA;EACER,SAAS,EAAErD,SAAS,CAAC8D,MAAM;EAC3B;AACF;AACA;EACER,KAAK,EAAEtD,SAAS,CAAC4D,IAAI;EACrB;AACF;AACA;EACEZ,OAAO,EAAEhD,SAAS,CAAC+D,IAAI,CAACC,UAAU;EAClC;AACF;AACA;EACEjC,KAAK,EAAE/B,SAAS,CAAC6D;AACnB,CAAC,GAAG,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}