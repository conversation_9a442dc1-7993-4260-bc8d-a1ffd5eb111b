{"ast": null, "code": "import style from \"../style/index.js\";\nconst boxShadow = style({\n  prop: 'boxShadow',\n  themeKey: 'shadows'\n});\nexport default boxShadow;", "map": {"version": 3, "names": ["style", "boxShadow", "prop", "<PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/vidcompressor/frontend/node_modules/@mui/system/esm/shadows/shadows.js"], "sourcesContent": ["import style from \"../style/index.js\";\nconst boxShadow = style({\n  prop: 'boxShadow',\n  themeKey: 'shadows'\n});\nexport default boxShadow;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,mBAAmB;AACrC,MAAMC,SAAS,GAAGD,KAAK,CAAC;EACtBE,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF,eAAeF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}