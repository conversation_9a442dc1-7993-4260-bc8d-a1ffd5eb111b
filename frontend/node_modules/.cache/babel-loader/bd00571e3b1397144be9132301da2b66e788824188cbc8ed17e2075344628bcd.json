{"ast": null, "code": "const getLegacyGridWarning = propName => {\n  if (['item', 'zeroMinWidth'].includes(propName)) {\n    return `The \\`${propName}\\` prop has been removed and is no longer necessary. You can safely remove it.`;\n  }\n\n  // #host-reference\n  return `The \\`${propName}\\` prop has been removed. See https://mui.com/material-ui/migration/upgrade-to-grid-v2/ for migration instructions.`;\n};\nconst warnedAboutProps = [];\n\n/**\n * Deletes the legacy Grid component props from the `props` object and warns once about them if found.\n *\n * @param {object} props The props object to remove the legacy Grid props from.\n * @param {Breakpoints} breakpoints The breakpoints object.\n */\nexport default function deleteLegacyGridProps(props, breakpoints) {\n  const propsToWarn = [];\n  if (props.item !== undefined) {\n    delete props.item;\n    propsToWarn.push('item');\n  }\n  if (props.zeroMinWidth !== undefined) {\n    delete props.zeroMinWidth;\n    propsToWarn.push('zeroMinWidth');\n  }\n  breakpoints.keys.forEach(breakpoint => {\n    if (props[breakpoint] !== undefined) {\n      propsToWarn.push(breakpoint);\n      delete props[breakpoint];\n    }\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    propsToWarn.forEach(prop => {\n      if (!warnedAboutProps.includes(prop)) {\n        warnedAboutProps.push(prop);\n        console.warn(`MUI Grid: ${getLegacyGridWarning(prop)}\\n`);\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["getLegacyGridWarning", "propName", "includes", "warnedAboutProps", "deleteLegacyGridProps", "props", "breakpoints", "props<PERSON><PERSON><PERSON><PERSON><PERSON>", "item", "undefined", "push", "zeroMinWidth", "keys", "for<PERSON>ach", "breakpoint", "process", "env", "NODE_ENV", "prop", "console", "warn"], "sources": ["/Users/<USER>/vidcompressor/frontend/node_modules/@mui/system/esm/Grid/deleteLegacyGridProps.js"], "sourcesContent": ["const getLegacyGridWarning = propName => {\n  if (['item', 'zeroMinWidth'].includes(propName)) {\n    return `The \\`${propName}\\` prop has been removed and is no longer necessary. You can safely remove it.`;\n  }\n\n  // #host-reference\n  return `The \\`${propName}\\` prop has been removed. See https://mui.com/material-ui/migration/upgrade-to-grid-v2/ for migration instructions.`;\n};\nconst warnedAboutProps = [];\n\n/**\n * Deletes the legacy Grid component props from the `props` object and warns once about them if found.\n *\n * @param {object} props The props object to remove the legacy Grid props from.\n * @param {Breakpoints} breakpoints The breakpoints object.\n */\nexport default function deleteLegacyGridProps(props, breakpoints) {\n  const propsToWarn = [];\n  if (props.item !== undefined) {\n    delete props.item;\n    propsToWarn.push('item');\n  }\n  if (props.zeroMinWidth !== undefined) {\n    delete props.zeroMinWidth;\n    propsToWarn.push('zeroMinWidth');\n  }\n  breakpoints.keys.forEach(breakpoint => {\n    if (props[breakpoint] !== undefined) {\n      propsToWarn.push(breakpoint);\n      delete props[breakpoint];\n    }\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    propsToWarn.forEach(prop => {\n      if (!warnedAboutProps.includes(prop)) {\n        warnedAboutProps.push(prop);\n        console.warn(`MUI Grid: ${getLegacyGridWarning(prop)}\\n`);\n      }\n    });\n  }\n}"], "mappings": "AAAA,MAAMA,oBAAoB,GAAGC,QAAQ,IAAI;EACvC,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,CAACC,QAAQ,CAACD,QAAQ,CAAC,EAAE;IAC/C,OAAO,SAASA,QAAQ,gFAAgF;EAC1G;;EAEA;EACA,OAAO,SAASA,QAAQ,qHAAqH;AAC/I,CAAC;AACD,MAAME,gBAAgB,GAAG,EAAE;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,qBAAqBA,CAACC,KAAK,EAAEC,WAAW,EAAE;EAChE,MAAMC,WAAW,GAAG,EAAE;EACtB,IAAIF,KAAK,CAACG,IAAI,KAAKC,SAAS,EAAE;IAC5B,OAAOJ,KAAK,CAACG,IAAI;IACjBD,WAAW,CAACG,IAAI,CAAC,MAAM,CAAC;EAC1B;EACA,IAAIL,KAAK,CAACM,YAAY,KAAKF,SAAS,EAAE;IACpC,OAAOJ,KAAK,CAACM,YAAY;IACzBJ,WAAW,CAACG,IAAI,CAAC,cAAc,CAAC;EAClC;EACAJ,WAAW,CAACM,IAAI,CAACC,OAAO,CAACC,UAAU,IAAI;IACrC,IAAIT,KAAK,CAACS,UAAU,CAAC,KAAKL,SAAS,EAAE;MACnCF,WAAW,CAACG,IAAI,CAACI,UAAU,CAAC;MAC5B,OAAOT,KAAK,CAACS,UAAU,CAAC;IAC1B;EACF,CAAC,CAAC;EACF,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCV,WAAW,CAACM,OAAO,CAACK,IAAI,IAAI;MAC1B,IAAI,CAACf,gBAAgB,CAACD,QAAQ,CAACgB,IAAI,CAAC,EAAE;QACpCf,gBAAgB,CAACO,IAAI,CAACQ,IAAI,CAAC;QAC3BC,OAAO,CAACC,IAAI,CAAC,aAAapB,oBAAoB,CAACkB,IAAI,CAAC,IAAI,CAAC;MAC3D;IACF,CAAC,CAAC;EACJ;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}