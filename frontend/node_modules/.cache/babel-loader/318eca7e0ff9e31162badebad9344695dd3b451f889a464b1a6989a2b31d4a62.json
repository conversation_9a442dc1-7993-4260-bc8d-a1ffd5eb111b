{"ast": null, "code": "/**\n * Removes event handlers from the given object.\n * A field is considered an event handler if it is a function with a name beginning with `on`.\n *\n * @param object Object to remove event handlers from.\n * @returns Object with event handlers removed.\n */\nfunction omitEventHandlers(object) {\n  if (object === undefined) {\n    return {};\n  }\n  const result = {};\n  Object.keys(object).filter(prop => !(prop.match(/^on[A-Z]/) && typeof object[prop] === 'function')).forEach(prop => {\n    result[prop] = object[prop];\n  });\n  return result;\n}\nexport default omitEventHandlers;", "map": {"version": 3, "names": ["omitEventHandlers", "object", "undefined", "result", "Object", "keys", "filter", "prop", "match", "for<PERSON>ach"], "sources": ["/Users/<USER>/vidcompressor/frontend/node_modules/@mui/utils/esm/omitEventHandlers/omitEventHandlers.js"], "sourcesContent": ["/**\n * Removes event handlers from the given object.\n * A field is considered an event handler if it is a function with a name beginning with `on`.\n *\n * @param object Object to remove event handlers from.\n * @returns Object with event handlers removed.\n */\nfunction omitEventHandlers(object) {\n  if (object === undefined) {\n    return {};\n  }\n  const result = {};\n  Object.keys(object).filter(prop => !(prop.match(/^on[A-Z]/) && typeof object[prop] === 'function')).forEach(prop => {\n    result[prop] = object[prop];\n  });\n  return result;\n}\nexport default omitEventHandlers;"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,iBAAiBA,CAACC,MAAM,EAAE;EACjC,IAAIA,MAAM,KAAKC,SAAS,EAAE;IACxB,OAAO,CAAC,CAAC;EACX;EACA,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjBC,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACK,MAAM,CAACC,IAAI,IAAI,EAAEA,IAAI,CAACC,KAAK,CAAC,UAAU,CAAC,IAAI,OAAOP,MAAM,CAACM,IAAI,CAAC,KAAK,UAAU,CAAC,CAAC,CAACE,OAAO,CAACF,IAAI,IAAI;IAClHJ,MAAM,CAACI,IAAI,CAAC,GAAGN,MAAM,CAACM,IAAI,CAAC;EAC7B,CAAC,CAAC;EACF,OAAOJ,MAAM;AACf;AACA,eAAeH,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}