{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { DEFAULT_MODE_STORAGE_KEY, DEFAULT_COLOR_SCHEME_STORAGE_KEY } from \"../InitColorSchemeScript/InitColorSchemeScript.js\";\nimport localStorageManager from \"./localStorageManager.js\";\nfunction noop() {}\nexport function getSystemMode(mode) {\n  if (typeof window !== 'undefined' && typeof window.matchMedia === 'function' && mode === 'system') {\n    const mql = window.matchMedia('(prefers-color-scheme: dark)');\n    if (mql.matches) {\n      return 'dark';\n    }\n    return 'light';\n  }\n  return undefined;\n}\nfunction processState(state, callback) {\n  if (state.mode === 'light' || state.mode === 'system' && state.systemMode === 'light') {\n    return callback('light');\n  }\n  if (state.mode === 'dark' || state.mode === 'system' && state.systemMode === 'dark') {\n    return callback('dark');\n  }\n  return undefined;\n}\nexport function getColorScheme(state) {\n  return processState(state, mode => {\n    if (mode === 'light') {\n      return state.lightColorScheme;\n    }\n    if (mode === 'dark') {\n      return state.darkColorScheme;\n    }\n    return undefined;\n  });\n}\nexport default function useCurrentColorScheme(options) {\n  const {\n    defaultMode = 'light',\n    defaultLightColorScheme,\n    defaultDarkColorScheme,\n    supportedColorSchemes = [],\n    modeStorageKey = DEFAULT_MODE_STORAGE_KEY,\n    colorSchemeStorageKey = DEFAULT_COLOR_SCHEME_STORAGE_KEY,\n    storageWindow = typeof window === 'undefined' ? undefined : window,\n    storageManager = localStorageManager,\n    noSsr = false\n  } = options;\n  const joinedColorSchemes = supportedColorSchemes.join(',');\n  const isMultiSchemes = supportedColorSchemes.length > 1;\n  const modeStorage = React.useMemo(() => storageManager?.({\n    key: modeStorageKey,\n    storageWindow\n  }), [storageManager, modeStorageKey, storageWindow]);\n  const lightStorage = React.useMemo(() => storageManager?.({\n    key: `${colorSchemeStorageKey}-light`,\n    storageWindow\n  }), [storageManager, colorSchemeStorageKey, storageWindow]);\n  const darkStorage = React.useMemo(() => storageManager?.({\n    key: `${colorSchemeStorageKey}-dark`,\n    storageWindow\n  }), [storageManager, colorSchemeStorageKey, storageWindow]);\n  const [state, setState] = React.useState(() => {\n    const initialMode = modeStorage?.get(defaultMode) || defaultMode;\n    const lightColorScheme = lightStorage?.get(defaultLightColorScheme) || defaultLightColorScheme;\n    const darkColorScheme = darkStorage?.get(defaultDarkColorScheme) || defaultDarkColorScheme;\n    return {\n      mode: initialMode,\n      systemMode: getSystemMode(initialMode),\n      lightColorScheme,\n      darkColorScheme\n    };\n  });\n  const [isClient, setIsClient] = React.useState(noSsr || !isMultiSchemes);\n  React.useEffect(() => {\n    setIsClient(true); // to rerender the component after hydration\n  }, []);\n  const colorScheme = getColorScheme(state);\n  const setMode = React.useCallback(mode => {\n    setState(currentState => {\n      if (mode === currentState.mode) {\n        // do nothing if mode does not change\n        return currentState;\n      }\n      const newMode = mode ?? defaultMode;\n      modeStorage?.set(newMode);\n      return {\n        ...currentState,\n        mode: newMode,\n        systemMode: getSystemMode(newMode)\n      };\n    });\n  }, [modeStorage, defaultMode]);\n  const setColorScheme = React.useCallback(value => {\n    if (!value) {\n      setState(currentState => {\n        lightStorage?.set(defaultLightColorScheme);\n        darkStorage?.set(defaultDarkColorScheme);\n        return {\n          ...currentState,\n          lightColorScheme: defaultLightColorScheme,\n          darkColorScheme: defaultDarkColorScheme\n        };\n      });\n    } else if (typeof value === 'string') {\n      if (value && !joinedColorSchemes.includes(value)) {\n        console.error(`\\`${value}\\` does not exist in \\`theme.colorSchemes\\`.`);\n      } else {\n        setState(currentState => {\n          const newState = {\n            ...currentState\n          };\n          processState(currentState, mode => {\n            if (mode === 'light') {\n              lightStorage?.set(value);\n              newState.lightColorScheme = value;\n            }\n            if (mode === 'dark') {\n              darkStorage?.set(value);\n              newState.darkColorScheme = value;\n            }\n          });\n          return newState;\n        });\n      }\n    } else {\n      setState(currentState => {\n        const newState = {\n          ...currentState\n        };\n        const newLightColorScheme = value.light === null ? defaultLightColorScheme : value.light;\n        const newDarkColorScheme = value.dark === null ? defaultDarkColorScheme : value.dark;\n        if (newLightColorScheme) {\n          if (!joinedColorSchemes.includes(newLightColorScheme)) {\n            console.error(`\\`${newLightColorScheme}\\` does not exist in \\`theme.colorSchemes\\`.`);\n          } else {\n            newState.lightColorScheme = newLightColorScheme;\n            lightStorage?.set(newLightColorScheme);\n          }\n        }\n        if (newDarkColorScheme) {\n          if (!joinedColorSchemes.includes(newDarkColorScheme)) {\n            console.error(`\\`${newDarkColorScheme}\\` does not exist in \\`theme.colorSchemes\\`.`);\n          } else {\n            newState.darkColorScheme = newDarkColorScheme;\n            darkStorage?.set(newDarkColorScheme);\n          }\n        }\n        return newState;\n      });\n    }\n  }, [joinedColorSchemes, lightStorage, darkStorage, defaultLightColorScheme, defaultDarkColorScheme]);\n  const handleMediaQuery = React.useCallback(event => {\n    if (state.mode === 'system') {\n      setState(currentState => {\n        const systemMode = event?.matches ? 'dark' : 'light';\n\n        // Early exit, nothing changed.\n        if (currentState.systemMode === systemMode) {\n          return currentState;\n        }\n        return {\n          ...currentState,\n          systemMode\n        };\n      });\n    }\n  }, [state.mode]);\n\n  // Ref hack to avoid adding handleMediaQuery as a dep\n  const mediaListener = React.useRef(handleMediaQuery);\n  mediaListener.current = handleMediaQuery;\n  React.useEffect(() => {\n    if (typeof window.matchMedia !== 'function' || !isMultiSchemes) {\n      return undefined;\n    }\n    const handler = (...args) => mediaListener.current(...args);\n\n    // Always listen to System preference\n    const media = window.matchMedia('(prefers-color-scheme: dark)');\n\n    // Intentionally use deprecated listener methods to support iOS & old browsers\n    media.addListener(handler);\n    handler(media);\n    return () => {\n      media.removeListener(handler);\n    };\n  }, [isMultiSchemes]);\n\n  // Handle when localStorage has changed\n  React.useEffect(() => {\n    if (isMultiSchemes) {\n      const unsubscribeMode = modeStorage?.subscribe(value => {\n        if (!value || ['light', 'dark', 'system'].includes(value)) {\n          setMode(value || defaultMode);\n        }\n      }) || noop;\n      const unsubscribeLight = lightStorage?.subscribe(value => {\n        if (!value || joinedColorSchemes.match(value)) {\n          setColorScheme({\n            light: value\n          });\n        }\n      }) || noop;\n      const unsubscribeDark = darkStorage?.subscribe(value => {\n        if (!value || joinedColorSchemes.match(value)) {\n          setColorScheme({\n            dark: value\n          });\n        }\n      }) || noop;\n      return () => {\n        unsubscribeMode();\n        unsubscribeLight();\n        unsubscribeDark();\n      };\n    }\n    return undefined;\n  }, [setColorScheme, setMode, joinedColorSchemes, defaultMode, storageWindow, isMultiSchemes, modeStorage, lightStorage, darkStorage]);\n  return {\n    ...state,\n    mode: isClient ? state.mode : undefined,\n    systemMode: isClient ? state.systemMode : undefined,\n    colorScheme: isClient ? colorScheme : undefined,\n    setMode,\n    setColorScheme\n  };\n}", "map": {"version": 3, "names": ["React", "DEFAULT_MODE_STORAGE_KEY", "DEFAULT_COLOR_SCHEME_STORAGE_KEY", "localStorageManager", "noop", "getSystemMode", "mode", "window", "matchMedia", "mql", "matches", "undefined", "processState", "state", "callback", "systemMode", "getColorScheme", "lightColorScheme", "darkColorScheme", "useCurrentColorScheme", "options", "defaultMode", "defaultLightColorScheme", "defaultDarkColorScheme", "supportedColorSchemes", "modeStorageKey", "colorSchemeStorageKey", "storageWindow", "storageManager", "noSsr", "joinedColorSchemes", "join", "isMultiSchemes", "length", "modeStorage", "useMemo", "key", "lightStorage", "darkStorage", "setState", "useState", "initialMode", "get", "isClient", "setIsClient", "useEffect", "colorScheme", "setMode", "useCallback", "currentState", "newMode", "set", "setColorScheme", "value", "includes", "console", "error", "newState", "newLightColorScheme", "light", "newDarkColorScheme", "dark", "handleMediaQuery", "event", "mediaListener", "useRef", "current", "handler", "args", "media", "addListener", "removeListener", "unsubscribeMode", "subscribe", "unsubscribeLight", "match", "unsubscribeDark"], "sources": ["/Users/<USER>/vidcompressor/frontend/node_modules/@mui/system/esm/cssVars/useCurrentColorScheme.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { DEFAULT_MODE_STORAGE_KEY, DEFAULT_COLOR_SCHEME_STORAGE_KEY } from \"../InitColorSchemeScript/InitColorSchemeScript.js\";\nimport localStorageManager from \"./localStorageManager.js\";\nfunction noop() {}\nexport function getSystemMode(mode) {\n  if (typeof window !== 'undefined' && typeof window.matchMedia === 'function' && mode === 'system') {\n    const mql = window.matchMedia('(prefers-color-scheme: dark)');\n    if (mql.matches) {\n      return 'dark';\n    }\n    return 'light';\n  }\n  return undefined;\n}\nfunction processState(state, callback) {\n  if (state.mode === 'light' || state.mode === 'system' && state.systemMode === 'light') {\n    return callback('light');\n  }\n  if (state.mode === 'dark' || state.mode === 'system' && state.systemMode === 'dark') {\n    return callback('dark');\n  }\n  return undefined;\n}\nexport function getColorScheme(state) {\n  return processState(state, mode => {\n    if (mode === 'light') {\n      return state.lightColorScheme;\n    }\n    if (mode === 'dark') {\n      return state.darkColorScheme;\n    }\n    return undefined;\n  });\n}\nexport default function useCurrentColorScheme(options) {\n  const {\n    defaultMode = 'light',\n    defaultLightColorScheme,\n    defaultDarkColorScheme,\n    supportedColorSchemes = [],\n    modeStorageKey = DEFAULT_MODE_STORAGE_KEY,\n    colorSchemeStorageKey = DEFAULT_COLOR_SCHEME_STORAGE_KEY,\n    storageWindow = typeof window === 'undefined' ? undefined : window,\n    storageManager = localStorageManager,\n    noSsr = false\n  } = options;\n  const joinedColorSchemes = supportedColorSchemes.join(',');\n  const isMultiSchemes = supportedColorSchemes.length > 1;\n  const modeStorage = React.useMemo(() => storageManager?.({\n    key: modeStorageKey,\n    storageWindow\n  }), [storageManager, modeStorageKey, storageWindow]);\n  const lightStorage = React.useMemo(() => storageManager?.({\n    key: `${colorSchemeStorageKey}-light`,\n    storageWindow\n  }), [storageManager, colorSchemeStorageKey, storageWindow]);\n  const darkStorage = React.useMemo(() => storageManager?.({\n    key: `${colorSchemeStorageKey}-dark`,\n    storageWindow\n  }), [storageManager, colorSchemeStorageKey, storageWindow]);\n  const [state, setState] = React.useState(() => {\n    const initialMode = modeStorage?.get(defaultMode) || defaultMode;\n    const lightColorScheme = lightStorage?.get(defaultLightColorScheme) || defaultLightColorScheme;\n    const darkColorScheme = darkStorage?.get(defaultDarkColorScheme) || defaultDarkColorScheme;\n    return {\n      mode: initialMode,\n      systemMode: getSystemMode(initialMode),\n      lightColorScheme,\n      darkColorScheme\n    };\n  });\n  const [isClient, setIsClient] = React.useState(noSsr || !isMultiSchemes);\n  React.useEffect(() => {\n    setIsClient(true); // to rerender the component after hydration\n  }, []);\n  const colorScheme = getColorScheme(state);\n  const setMode = React.useCallback(mode => {\n    setState(currentState => {\n      if (mode === currentState.mode) {\n        // do nothing if mode does not change\n        return currentState;\n      }\n      const newMode = mode ?? defaultMode;\n      modeStorage?.set(newMode);\n      return {\n        ...currentState,\n        mode: newMode,\n        systemMode: getSystemMode(newMode)\n      };\n    });\n  }, [modeStorage, defaultMode]);\n  const setColorScheme = React.useCallback(value => {\n    if (!value) {\n      setState(currentState => {\n        lightStorage?.set(defaultLightColorScheme);\n        darkStorage?.set(defaultDarkColorScheme);\n        return {\n          ...currentState,\n          lightColorScheme: defaultLightColorScheme,\n          darkColorScheme: defaultDarkColorScheme\n        };\n      });\n    } else if (typeof value === 'string') {\n      if (value && !joinedColorSchemes.includes(value)) {\n        console.error(`\\`${value}\\` does not exist in \\`theme.colorSchemes\\`.`);\n      } else {\n        setState(currentState => {\n          const newState = {\n            ...currentState\n          };\n          processState(currentState, mode => {\n            if (mode === 'light') {\n              lightStorage?.set(value);\n              newState.lightColorScheme = value;\n            }\n            if (mode === 'dark') {\n              darkStorage?.set(value);\n              newState.darkColorScheme = value;\n            }\n          });\n          return newState;\n        });\n      }\n    } else {\n      setState(currentState => {\n        const newState = {\n          ...currentState\n        };\n        const newLightColorScheme = value.light === null ? defaultLightColorScheme : value.light;\n        const newDarkColorScheme = value.dark === null ? defaultDarkColorScheme : value.dark;\n        if (newLightColorScheme) {\n          if (!joinedColorSchemes.includes(newLightColorScheme)) {\n            console.error(`\\`${newLightColorScheme}\\` does not exist in \\`theme.colorSchemes\\`.`);\n          } else {\n            newState.lightColorScheme = newLightColorScheme;\n            lightStorage?.set(newLightColorScheme);\n          }\n        }\n        if (newDarkColorScheme) {\n          if (!joinedColorSchemes.includes(newDarkColorScheme)) {\n            console.error(`\\`${newDarkColorScheme}\\` does not exist in \\`theme.colorSchemes\\`.`);\n          } else {\n            newState.darkColorScheme = newDarkColorScheme;\n            darkStorage?.set(newDarkColorScheme);\n          }\n        }\n        return newState;\n      });\n    }\n  }, [joinedColorSchemes, lightStorage, darkStorage, defaultLightColorScheme, defaultDarkColorScheme]);\n  const handleMediaQuery = React.useCallback(event => {\n    if (state.mode === 'system') {\n      setState(currentState => {\n        const systemMode = event?.matches ? 'dark' : 'light';\n\n        // Early exit, nothing changed.\n        if (currentState.systemMode === systemMode) {\n          return currentState;\n        }\n        return {\n          ...currentState,\n          systemMode\n        };\n      });\n    }\n  }, [state.mode]);\n\n  // Ref hack to avoid adding handleMediaQuery as a dep\n  const mediaListener = React.useRef(handleMediaQuery);\n  mediaListener.current = handleMediaQuery;\n  React.useEffect(() => {\n    if (typeof window.matchMedia !== 'function' || !isMultiSchemes) {\n      return undefined;\n    }\n    const handler = (...args) => mediaListener.current(...args);\n\n    // Always listen to System preference\n    const media = window.matchMedia('(prefers-color-scheme: dark)');\n\n    // Intentionally use deprecated listener methods to support iOS & old browsers\n    media.addListener(handler);\n    handler(media);\n    return () => {\n      media.removeListener(handler);\n    };\n  }, [isMultiSchemes]);\n\n  // Handle when localStorage has changed\n  React.useEffect(() => {\n    if (isMultiSchemes) {\n      const unsubscribeMode = modeStorage?.subscribe(value => {\n        if (!value || ['light', 'dark', 'system'].includes(value)) {\n          setMode(value || defaultMode);\n        }\n      }) || noop;\n      const unsubscribeLight = lightStorage?.subscribe(value => {\n        if (!value || joinedColorSchemes.match(value)) {\n          setColorScheme({\n            light: value\n          });\n        }\n      }) || noop;\n      const unsubscribeDark = darkStorage?.subscribe(value => {\n        if (!value || joinedColorSchemes.match(value)) {\n          setColorScheme({\n            dark: value\n          });\n        }\n      }) || noop;\n      return () => {\n        unsubscribeMode();\n        unsubscribeLight();\n        unsubscribeDark();\n      };\n    }\n    return undefined;\n  }, [setColorScheme, setMode, joinedColorSchemes, defaultMode, storageWindow, isMultiSchemes, modeStorage, lightStorage, darkStorage]);\n  return {\n    ...state,\n    mode: isClient ? state.mode : undefined,\n    systemMode: isClient ? state.systemMode : undefined,\n    colorScheme: isClient ? colorScheme : undefined,\n    setMode,\n    setColorScheme\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,wBAAwB,EAAEC,gCAAgC,QAAQ,mDAAmD;AAC9H,OAAOC,mBAAmB,MAAM,0BAA0B;AAC1D,SAASC,IAAIA,CAAA,EAAG,CAAC;AACjB,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAE;EAClC,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACC,UAAU,KAAK,UAAU,IAAIF,IAAI,KAAK,QAAQ,EAAE;IACjG,MAAMG,GAAG,GAAGF,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC;IAC7D,IAAIC,GAAG,CAACC,OAAO,EAAE;MACf,OAAO,MAAM;IACf;IACA,OAAO,OAAO;EAChB;EACA,OAAOC,SAAS;AAClB;AACA,SAASC,YAAYA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EACrC,IAAID,KAAK,CAACP,IAAI,KAAK,OAAO,IAAIO,KAAK,CAACP,IAAI,KAAK,QAAQ,IAAIO,KAAK,CAACE,UAAU,KAAK,OAAO,EAAE;IACrF,OAAOD,QAAQ,CAAC,OAAO,CAAC;EAC1B;EACA,IAAID,KAAK,CAACP,IAAI,KAAK,MAAM,IAAIO,KAAK,CAACP,IAAI,KAAK,QAAQ,IAAIO,KAAK,CAACE,UAAU,KAAK,MAAM,EAAE;IACnF,OAAOD,QAAQ,CAAC,MAAM,CAAC;EACzB;EACA,OAAOH,SAAS;AAClB;AACA,OAAO,SAASK,cAAcA,CAACH,KAAK,EAAE;EACpC,OAAOD,YAAY,CAACC,KAAK,EAAEP,IAAI,IAAI;IACjC,IAAIA,IAAI,KAAK,OAAO,EAAE;MACpB,OAAOO,KAAK,CAACI,gBAAgB;IAC/B;IACA,IAAIX,IAAI,KAAK,MAAM,EAAE;MACnB,OAAOO,KAAK,CAACK,eAAe;IAC9B;IACA,OAAOP,SAAS;EAClB,CAAC,CAAC;AACJ;AACA,eAAe,SAASQ,qBAAqBA,CAACC,OAAO,EAAE;EACrD,MAAM;IACJC,WAAW,GAAG,OAAO;IACrBC,uBAAuB;IACvBC,sBAAsB;IACtBC,qBAAqB,GAAG,EAAE;IAC1BC,cAAc,GAAGxB,wBAAwB;IACzCyB,qBAAqB,GAAGxB,gCAAgC;IACxDyB,aAAa,GAAG,OAAOpB,MAAM,KAAK,WAAW,GAAGI,SAAS,GAAGJ,MAAM;IAClEqB,cAAc,GAAGzB,mBAAmB;IACpC0B,KAAK,GAAG;EACV,CAAC,GAAGT,OAAO;EACX,MAAMU,kBAAkB,GAAGN,qBAAqB,CAACO,IAAI,CAAC,GAAG,CAAC;EAC1D,MAAMC,cAAc,GAAGR,qBAAqB,CAACS,MAAM,GAAG,CAAC;EACvD,MAAMC,WAAW,GAAGlC,KAAK,CAACmC,OAAO,CAAC,MAAMP,cAAc,GAAG;IACvDQ,GAAG,EAAEX,cAAc;IACnBE;EACF,CAAC,CAAC,EAAE,CAACC,cAAc,EAAEH,cAAc,EAAEE,aAAa,CAAC,CAAC;EACpD,MAAMU,YAAY,GAAGrC,KAAK,CAACmC,OAAO,CAAC,MAAMP,cAAc,GAAG;IACxDQ,GAAG,EAAE,GAAGV,qBAAqB,QAAQ;IACrCC;EACF,CAAC,CAAC,EAAE,CAACC,cAAc,EAAEF,qBAAqB,EAAEC,aAAa,CAAC,CAAC;EAC3D,MAAMW,WAAW,GAAGtC,KAAK,CAACmC,OAAO,CAAC,MAAMP,cAAc,GAAG;IACvDQ,GAAG,EAAE,GAAGV,qBAAqB,OAAO;IACpCC;EACF,CAAC,CAAC,EAAE,CAACC,cAAc,EAAEF,qBAAqB,EAAEC,aAAa,CAAC,CAAC;EAC3D,MAAM,CAACd,KAAK,EAAE0B,QAAQ,CAAC,GAAGvC,KAAK,CAACwC,QAAQ,CAAC,MAAM;IAC7C,MAAMC,WAAW,GAAGP,WAAW,EAAEQ,GAAG,CAACrB,WAAW,CAAC,IAAIA,WAAW;IAChE,MAAMJ,gBAAgB,GAAGoB,YAAY,EAAEK,GAAG,CAACpB,uBAAuB,CAAC,IAAIA,uBAAuB;IAC9F,MAAMJ,eAAe,GAAGoB,WAAW,EAAEI,GAAG,CAACnB,sBAAsB,CAAC,IAAIA,sBAAsB;IAC1F,OAAO;MACLjB,IAAI,EAAEmC,WAAW;MACjB1B,UAAU,EAAEV,aAAa,CAACoC,WAAW,CAAC;MACtCxB,gBAAgB;MAChBC;IACF,CAAC;EACH,CAAC,CAAC;EACF,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG5C,KAAK,CAACwC,QAAQ,CAACX,KAAK,IAAI,CAACG,cAAc,CAAC;EACxEhC,KAAK,CAAC6C,SAAS,CAAC,MAAM;IACpBD,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EACN,MAAME,WAAW,GAAG9B,cAAc,CAACH,KAAK,CAAC;EACzC,MAAMkC,OAAO,GAAG/C,KAAK,CAACgD,WAAW,CAAC1C,IAAI,IAAI;IACxCiC,QAAQ,CAACU,YAAY,IAAI;MACvB,IAAI3C,IAAI,KAAK2C,YAAY,CAAC3C,IAAI,EAAE;QAC9B;QACA,OAAO2C,YAAY;MACrB;MACA,MAAMC,OAAO,GAAG5C,IAAI,IAAIe,WAAW;MACnCa,WAAW,EAAEiB,GAAG,CAACD,OAAO,CAAC;MACzB,OAAO;QACL,GAAGD,YAAY;QACf3C,IAAI,EAAE4C,OAAO;QACbnC,UAAU,EAAEV,aAAa,CAAC6C,OAAO;MACnC,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAAChB,WAAW,EAAEb,WAAW,CAAC,CAAC;EAC9B,MAAM+B,cAAc,GAAGpD,KAAK,CAACgD,WAAW,CAACK,KAAK,IAAI;IAChD,IAAI,CAACA,KAAK,EAAE;MACVd,QAAQ,CAACU,YAAY,IAAI;QACvBZ,YAAY,EAAEc,GAAG,CAAC7B,uBAAuB,CAAC;QAC1CgB,WAAW,EAAEa,GAAG,CAAC5B,sBAAsB,CAAC;QACxC,OAAO;UACL,GAAG0B,YAAY;UACfhC,gBAAgB,EAAEK,uBAAuB;UACzCJ,eAAe,EAAEK;QACnB,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,OAAO8B,KAAK,KAAK,QAAQ,EAAE;MACpC,IAAIA,KAAK,IAAI,CAACvB,kBAAkB,CAACwB,QAAQ,CAACD,KAAK,CAAC,EAAE;QAChDE,OAAO,CAACC,KAAK,CAAC,KAAKH,KAAK,8CAA8C,CAAC;MACzE,CAAC,MAAM;QACLd,QAAQ,CAACU,YAAY,IAAI;UACvB,MAAMQ,QAAQ,GAAG;YACf,GAAGR;UACL,CAAC;UACDrC,YAAY,CAACqC,YAAY,EAAE3C,IAAI,IAAI;YACjC,IAAIA,IAAI,KAAK,OAAO,EAAE;cACpB+B,YAAY,EAAEc,GAAG,CAACE,KAAK,CAAC;cACxBI,QAAQ,CAACxC,gBAAgB,GAAGoC,KAAK;YACnC;YACA,IAAI/C,IAAI,KAAK,MAAM,EAAE;cACnBgC,WAAW,EAAEa,GAAG,CAACE,KAAK,CAAC;cACvBI,QAAQ,CAACvC,eAAe,GAAGmC,KAAK;YAClC;UACF,CAAC,CAAC;UACF,OAAOI,QAAQ;QACjB,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACLlB,QAAQ,CAACU,YAAY,IAAI;QACvB,MAAMQ,QAAQ,GAAG;UACf,GAAGR;QACL,CAAC;QACD,MAAMS,mBAAmB,GAAGL,KAAK,CAACM,KAAK,KAAK,IAAI,GAAGrC,uBAAuB,GAAG+B,KAAK,CAACM,KAAK;QACxF,MAAMC,kBAAkB,GAAGP,KAAK,CAACQ,IAAI,KAAK,IAAI,GAAGtC,sBAAsB,GAAG8B,KAAK,CAACQ,IAAI;QACpF,IAAIH,mBAAmB,EAAE;UACvB,IAAI,CAAC5B,kBAAkB,CAACwB,QAAQ,CAACI,mBAAmB,CAAC,EAAE;YACrDH,OAAO,CAACC,KAAK,CAAC,KAAKE,mBAAmB,8CAA8C,CAAC;UACvF,CAAC,MAAM;YACLD,QAAQ,CAACxC,gBAAgB,GAAGyC,mBAAmB;YAC/CrB,YAAY,EAAEc,GAAG,CAACO,mBAAmB,CAAC;UACxC;QACF;QACA,IAAIE,kBAAkB,EAAE;UACtB,IAAI,CAAC9B,kBAAkB,CAACwB,QAAQ,CAACM,kBAAkB,CAAC,EAAE;YACpDL,OAAO,CAACC,KAAK,CAAC,KAAKI,kBAAkB,8CAA8C,CAAC;UACtF,CAAC,MAAM;YACLH,QAAQ,CAACvC,eAAe,GAAG0C,kBAAkB;YAC7CtB,WAAW,EAAEa,GAAG,CAACS,kBAAkB,CAAC;UACtC;QACF;QACA,OAAOH,QAAQ;MACjB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC3B,kBAAkB,EAAEO,YAAY,EAAEC,WAAW,EAAEhB,uBAAuB,EAAEC,sBAAsB,CAAC,CAAC;EACpG,MAAMuC,gBAAgB,GAAG9D,KAAK,CAACgD,WAAW,CAACe,KAAK,IAAI;IAClD,IAAIlD,KAAK,CAACP,IAAI,KAAK,QAAQ,EAAE;MAC3BiC,QAAQ,CAACU,YAAY,IAAI;QACvB,MAAMlC,UAAU,GAAGgD,KAAK,EAAErD,OAAO,GAAG,MAAM,GAAG,OAAO;;QAEpD;QACA,IAAIuC,YAAY,CAAClC,UAAU,KAAKA,UAAU,EAAE;UAC1C,OAAOkC,YAAY;QACrB;QACA,OAAO;UACL,GAAGA,YAAY;UACflC;QACF,CAAC;MACH,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACF,KAAK,CAACP,IAAI,CAAC,CAAC;;EAEhB;EACA,MAAM0D,aAAa,GAAGhE,KAAK,CAACiE,MAAM,CAACH,gBAAgB,CAAC;EACpDE,aAAa,CAACE,OAAO,GAAGJ,gBAAgB;EACxC9D,KAAK,CAAC6C,SAAS,CAAC,MAAM;IACpB,IAAI,OAAOtC,MAAM,CAACC,UAAU,KAAK,UAAU,IAAI,CAACwB,cAAc,EAAE;MAC9D,OAAOrB,SAAS;IAClB;IACA,MAAMwD,OAAO,GAAGA,CAAC,GAAGC,IAAI,KAAKJ,aAAa,CAACE,OAAO,CAAC,GAAGE,IAAI,CAAC;;IAE3D;IACA,MAAMC,KAAK,GAAG9D,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC;;IAE/D;IACA6D,KAAK,CAACC,WAAW,CAACH,OAAO,CAAC;IAC1BA,OAAO,CAACE,KAAK,CAAC;IACd,OAAO,MAAM;MACXA,KAAK,CAACE,cAAc,CAACJ,OAAO,CAAC;IAC/B,CAAC;EACH,CAAC,EAAE,CAACnC,cAAc,CAAC,CAAC;;EAEpB;EACAhC,KAAK,CAAC6C,SAAS,CAAC,MAAM;IACpB,IAAIb,cAAc,EAAE;MAClB,MAAMwC,eAAe,GAAGtC,WAAW,EAAEuC,SAAS,CAACpB,KAAK,IAAI;QACtD,IAAI,CAACA,KAAK,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAACC,QAAQ,CAACD,KAAK,CAAC,EAAE;UACzDN,OAAO,CAACM,KAAK,IAAIhC,WAAW,CAAC;QAC/B;MACF,CAAC,CAAC,IAAIjB,IAAI;MACV,MAAMsE,gBAAgB,GAAGrC,YAAY,EAAEoC,SAAS,CAACpB,KAAK,IAAI;QACxD,IAAI,CAACA,KAAK,IAAIvB,kBAAkB,CAAC6C,KAAK,CAACtB,KAAK,CAAC,EAAE;UAC7CD,cAAc,CAAC;YACbO,KAAK,EAAEN;UACT,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,IAAIjD,IAAI;MACV,MAAMwE,eAAe,GAAGtC,WAAW,EAAEmC,SAAS,CAACpB,KAAK,IAAI;QACtD,IAAI,CAACA,KAAK,IAAIvB,kBAAkB,CAAC6C,KAAK,CAACtB,KAAK,CAAC,EAAE;UAC7CD,cAAc,CAAC;YACbS,IAAI,EAAER;UACR,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,IAAIjD,IAAI;MACV,OAAO,MAAM;QACXoE,eAAe,CAAC,CAAC;QACjBE,gBAAgB,CAAC,CAAC;QAClBE,eAAe,CAAC,CAAC;MACnB,CAAC;IACH;IACA,OAAOjE,SAAS;EAClB,CAAC,EAAE,CAACyC,cAAc,EAAEL,OAAO,EAAEjB,kBAAkB,EAAET,WAAW,EAAEM,aAAa,EAAEK,cAAc,EAAEE,WAAW,EAAEG,YAAY,EAAEC,WAAW,CAAC,CAAC;EACrI,OAAO;IACL,GAAGzB,KAAK;IACRP,IAAI,EAAEqC,QAAQ,GAAG9B,KAAK,CAACP,IAAI,GAAGK,SAAS;IACvCI,UAAU,EAAE4B,QAAQ,GAAG9B,KAAK,CAACE,UAAU,GAAGJ,SAAS;IACnDmC,WAAW,EAAEH,QAAQ,GAAGG,WAAW,GAAGnC,SAAS;IAC/CoC,OAAO;IACPK;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}