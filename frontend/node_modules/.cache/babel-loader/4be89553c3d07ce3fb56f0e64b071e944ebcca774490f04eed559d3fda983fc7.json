{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/vidcompressor/frontend/src/App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { GoogleOAuthProvider, GoogleLogin } from '@react-oauth/google';\nimport { AppBar, Toolbar, Typography, Button, Container, Grid, Paper } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst App = () => {\n  _s();\n  const [user, setUser] = useState(null);\n  const handleLoginSuccess = credentialResponse => {\n    setUser(credentialResponse);\n    // Here you would typically send the token to your backend for verification\n    console.log('Login Success:', credentialResponse);\n  };\n  const handleLoginError = () => {\n    console.log('Login Failed');\n  };\n  const handleLogout = () => {\n    setUser(null);\n  };\n  return /*#__PURE__*/_jsxDEV(GoogleOAuthProvider, {\n    clientId: \"YOUR_GOOGLE_CLIENT_ID\",\n    children: [/*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"static\",\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          component: \"div\",\n          sx: {\n            flexGrow: 1\n          },\n          children: \"VidCompressor\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this), user ? /*#__PURE__*/_jsxDEV(Button, {\n          color: \"inherit\",\n          onClick: handleLogout,\n          children: \"Logout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(GoogleLogin, {\n          onSuccess: handleLoginSuccess,\n          onError: handleLoginError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      sx: {\n        mt: 4\n      },\n      children: user ? /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          gutterBottom: true,\n          children: \"Your Video Library\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            size: {\n              xs: 12,\n              sm: 6,\n              md: 4\n            },\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              sx: {\n                p: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                children: \"Video 1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            size: {\n              xs: 12,\n              sm: 6,\n              md: 4\n            },\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              sx: {\n                p: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                children: \"Video 2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            size: {\n              xs: 12,\n              sm: 6,\n              md: 4\n            },\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              sx: {\n                p: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                children: \"Video 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        align: \"center\",\n        sx: {\n          mt: 10\n        },\n        children: \"Please sign in with your Google account to continue.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n};\n_s(App, \"Iei9RGtZU29Y1RhBe1sbfh/MntA=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "GoogleOAuthProvider", "GoogleLogin", "AppBar", "<PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Container", "Grid", "Paper", "jsxDEV", "_jsxDEV", "App", "_s", "user", "setUser", "handleLoginSuccess", "credentialResponse", "console", "log", "handleLoginError", "handleLogout", "clientId", "children", "position", "variant", "component", "sx", "flexGrow", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "onClick", "onSuccess", "onError", "mt", "gutterBottom", "container", "spacing", "size", "xs", "sm", "md", "p", "align", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/vidcompressor/frontend/src/App.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { GoogleOAuthProvider, GoogleLogin, CredentialResponse } from '@react-oauth/google';\nimport { AppBar, Toolbar, Typography, Button, Container, Grid, Paper } from '@mui/material';\n\nconst App: React.FC = () => {\n  const [user, setUser] = useState<CredentialResponse | null>(null);\n\n  const handleLoginSuccess = (credentialResponse: CredentialResponse) => {\n    setUser(credentialResponse);\n    // Here you would typically send the token to your backend for verification\n    console.log('Login Success:', credentialResponse);\n  };\n\n  const handleLoginError = () => {\n    console.log('Login Failed');\n  };\n\n  const handleLogout = () => {\n    setUser(null);\n  };\n\n  return (\n    <GoogleOAuthProvider clientId=\"YOUR_GOOGLE_CLIENT_ID\">\n      <AppBar position=\"static\">\n        <Toolbar>\n          <Typography variant=\"h6\" component=\"div\" sx={{ flexGrow: 1 }}>\n            VidCompressor\n          </Typography>\n          {user ? (\n            <Button color=\"inherit\" onClick={handleLogout}>Logout</Button>\n          ) : (\n            <GoogleLogin onSuccess={handleLoginSuccess} onError={handleLoginError} />\n          )}\n        </Toolbar>\n      </AppBar>\n      <Container sx={{ mt: 4 }}>\n        {user ? (\n          <div>\n            <Typography variant=\"h4\" gutterBottom>\n              Your Video Library\n            </Typography>\n            <Grid container spacing={2}>\n              {/* Placeholder for video gallery */}\n              <Grid size={{ xs: 12, sm: 6, md: 4 }}>\n                <Paper sx={{ p: 2 }}>\n                  <Typography>Video 1</Typography>\n                </Paper>\n              </Grid>\n              <Grid size={{ xs: 12, sm: 6, md: 4 }}>\n                <Paper sx={{ p: 2 }}>\n                  <Typography>Video 2</Typography>\n                </Paper>\n              </Grid>\n              <Grid size={{ xs: 12, sm: 6, md: 4 }}>\n                <Paper sx={{ p: 2 }}>\n                  <Typography>Video 3</Typography>\n                </Paper>\n              </Grid>\n            </Grid>\n          </div>\n        ) : (\n          <Typography variant=\"h5\" align=\"center\" sx={{ mt: 10 }}>\n            Please sign in with your Google account to continue.\n          </Typography>\n        )}\n      </Container>\n    </GoogleOAuthProvider>\n  );\n};\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,mBAAmB,EAAEC,WAAW,QAA4B,qBAAqB;AAC1F,SAASC,MAAM,EAAEC,OAAO,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,IAAI,EAAEC,KAAK,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5F,MAAMC,GAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGf,QAAQ,CAA4B,IAAI,CAAC;EAEjE,MAAMgB,kBAAkB,GAAIC,kBAAsC,IAAK;IACrEF,OAAO,CAACE,kBAAkB,CAAC;IAC3B;IACAC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEF,kBAAkB,CAAC;EACnD,CAAC;EAED,MAAMG,gBAAgB,GAAGA,CAAA,KAAM;IAC7BF,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAC7B,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzBN,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;EAED,oBACEJ,OAAA,CAACV,mBAAmB;IAACqB,QAAQ,EAAC,uBAAuB;IAAAC,QAAA,gBACnDZ,OAAA,CAACR,MAAM;MAACqB,QAAQ,EAAC,QAAQ;MAAAD,QAAA,eACvBZ,OAAA,CAACP,OAAO;QAAAmB,QAAA,gBACNZ,OAAA,CAACN,UAAU;UAACoB,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,KAAK;UAACC,EAAE,EAAE;YAAEC,QAAQ,EAAE;UAAE,CAAE;UAAAL,QAAA,EAAC;QAE9D;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZlB,IAAI,gBACHH,OAAA,CAACL,MAAM;UAAC2B,KAAK,EAAC,SAAS;UAACC,OAAO,EAAEb,YAAa;UAAAE,QAAA,EAAC;QAAM;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,gBAE9DrB,OAAA,CAACT,WAAW;UAACiC,SAAS,EAAEnB,kBAAmB;UAACoB,OAAO,EAAEhB;QAAiB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACzE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACTrB,OAAA,CAACJ,SAAS;MAACoB,EAAE,EAAE;QAAEU,EAAE,EAAE;MAAE,CAAE;MAAAd,QAAA,EACtBT,IAAI,gBACHH,OAAA;QAAAY,QAAA,gBACEZ,OAAA,CAACN,UAAU;UAACoB,OAAO,EAAC,IAAI;UAACa,YAAY;UAAAf,QAAA,EAAC;QAEtC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbrB,OAAA,CAACH,IAAI;UAAC+B,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAjB,QAAA,gBAEzBZ,OAAA,CAACH,IAAI;YAACiC,IAAI,EAAE;cAAEC,EAAE,EAAE,EAAE;cAAEC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAArB,QAAA,eACnCZ,OAAA,CAACF,KAAK;cAACkB,EAAE,EAAE;gBAAEkB,CAAC,EAAE;cAAE,CAAE;cAAAtB,QAAA,eAClBZ,OAAA,CAACN,UAAU;gBAAAkB,QAAA,EAAC;cAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACPrB,OAAA,CAACH,IAAI;YAACiC,IAAI,EAAE;cAAEC,EAAE,EAAE,EAAE;cAAEC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAArB,QAAA,eACnCZ,OAAA,CAACF,KAAK;cAACkB,EAAE,EAAE;gBAAEkB,CAAC,EAAE;cAAE,CAAE;cAAAtB,QAAA,eAClBZ,OAAA,CAACN,UAAU;gBAAAkB,QAAA,EAAC;cAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACPrB,OAAA,CAACH,IAAI;YAACiC,IAAI,EAAE;cAAEC,EAAE,EAAE,EAAE;cAAEC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAArB,QAAA,eACnCZ,OAAA,CAACF,KAAK;cAACkB,EAAE,EAAE;gBAAEkB,CAAC,EAAE;cAAE,CAAE;cAAAtB,QAAA,eAClBZ,OAAA,CAACN,UAAU;gBAAAkB,QAAA,EAAC;cAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,gBAENrB,OAAA,CAACN,UAAU;QAACoB,OAAO,EAAC,IAAI;QAACqB,KAAK,EAAC,QAAQ;QAACnB,EAAE,EAAE;UAAEU,EAAE,EAAE;QAAG,CAAE;QAAAd,QAAA,EAAC;MAExD;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAE1B,CAAC;AAACnB,EAAA,CAhEID,GAAa;AAAAmC,EAAA,GAAbnC,GAAa;AAkEnB,eAAeA,GAAG;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}